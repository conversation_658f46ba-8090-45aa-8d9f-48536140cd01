{% sw_extends '@Storefront/storefront/component/account/account-sidebar.html.twig' %}

{% block component_account_sidebar_nav_items %}
    {{ parent() }}
    
    {% if page.extensions.bsShowUserManagement %}
        <div class="account-sidebar-item">
            <a href="{{ path('frontend.account.user.management') }}" 
               class="account-sidebar-link{% if controllerAction == 'userManagementPage' %} is-active{% endif %}"
               title="{{ "account.userManagement.title"|trans|sw_sanitize }}">
                <span class="account-sidebar-link-icon">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path>
                        <circle cx="9" cy="7" r="4"></circle>
                        <path d="M22 21v-2a4 4 0 0 0-3-3.87"></path>
                        <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
                    </svg>
                </span>
                <span class="account-sidebar-link-text">{{ "account.userManagement.title"|trans|sw_sanitize }}</span>
            </a>
        </div>
    {% endif %}
{% endblock %}
