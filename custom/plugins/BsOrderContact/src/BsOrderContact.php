<?php declare(strict_types=1);

namespace Bs\OrderContact;

use Bs\OrderContact\Service\CustomFieldService;
use Shopware\Core\Framework\Plugin;
use Shopware\Core\Framework\Plugin\Context\ActivateContext;
use Shopware\Core\Framework\Plugin\Context\DeactivateContext;
use Shopware\Core\Framework\Plugin\Context\InstallContext;
use Shopware\Core\Framework\Plugin\Context\UninstallContext;

/**
 * Class BsOrderContact
 * @package BsOrderContact
 * <AUTHOR>
 * @link www.brainstreamtechnolabs.com
 */
class BsOrderContact extends Plugin
{
    /**
     * @param InstallContext $installContext
     * @return void
     */
    public function install(InstallContext $installContext): void
    {
        parent::install($installContext);

        // Run migrations
        $migrationCollection = $installContext->getMigrationCollection();
        $migrationCollection->migrateInPlace();
    }

    /**
     * @param UninstallContext $uninstallContext
     * @return void
     */
    public function uninstall(UninstallContext $uninstallContext): void
    {
        parent::uninstall($uninstallContext);

        if ($uninstallContext->keepUserData()) {
            return;
        }

        // Clean up database tables if user data should not be kept
        $connection = \Shopware\Core\Kernel::getConnection();
        $connection->executeStatement('DROP TABLE IF EXISTS `bs_order_contact_person`');

        // Note: Custom fields in orders are preserved as they're part of the core order data
        // If you want to clean them up, you would need to update all orders to remove the custom fields
    }

    /**
     * @param ActivateContext $activateContext
     * @return void
     */
    public function activate(ActivateContext $activateContext): void
    {
        parent::activate($activateContext);
    }

    /**
     * @param DeactivateContext $deactivateContext
     * @return void
     */
    public function deactivate(DeactivateContext $deactivateContext): void
    {
        parent::deactivate($deactivateContext);
    }
}
