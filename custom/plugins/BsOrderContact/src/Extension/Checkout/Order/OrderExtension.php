<?php declare(strict_types=1);

namespace Bs\OrderContact\Extension\Checkout\Order;

use Bs\OrderContact\Core\Content\ContactPerson\ContactPersonDefinition;
use Shopware\Core\Checkout\Order\OrderDefinition;
use Shopware\Core\Framework\DataAbstractionLayer\EntityExtension;
use Shopware\Core\Framework\DataAbstractionLayer\Field\FkField;
use Shopware\Core\Framework\DataAbstractionLayer\Field\ManyToOneAssociationField;
use Shopware\Core\Framework\DataAbstractionLayer\Field\StringField;
use Shopware\Core\Framework\DataAbstractionLayer\FieldCollection;

/**
 * Class OrderExtension
 * @package BsOrderContact
 * <AUTHOR>
 * @link www.brainstreamtechnolabs.com
 */
class OrderExtension extends EntityExtension
{
    public function extendFields(FieldCollection $collection): void
    {
        $collection->add(
            new StringField('bs_contact_person_id', 'bsContactPersonId')
        );
        
        $collection->add(
            new StringField('bs_contact_person_name', 'bsContactPersonName')
        );
        
        $collection->add(
            new ManyToOneAssociationField(
                'bsContactPerson',
                'bs_contact_person_id',
                ContactPersonDefinition::class,
                'id',
                false
            )
        );
    }

    public function getDefinitionClass(): string
    {
        return OrderDefinition::class;
    }
}
