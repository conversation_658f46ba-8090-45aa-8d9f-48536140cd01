<?php declare(strict_types=1);

namespace Bs\OrderContact\Subscriber;

use Bs\OrderContact\Service\UserManagementService;
use Shopware\Storefront\Event\RouteRequest\HandlePaymentMethodRouteRequestEvent;
use Shopware\Storefront\Page\Account\Overview\AccountOverviewPageLoadedEvent;
use Shopware\Storefront\Pagelet\Header\HeaderPageletLoadedEvent;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;

/**
 * Class AccountMenuSubscriber
 * @package BsOrderContact
 * <AUTHOR>
 * @link www.brainstreamtechnolabs.com
 */
class AccountMenuSubscriber implements EventSubscriberInterface
{
    private UserManagementService $userManagementService;

    public function __construct(UserManagementService $userManagementService)
    {
        $this->userManagementService = $userManagementService;
    }

    public static function getSubscribedEvents(): array
    {
        return [
            AccountOverviewPageLoadedEvent::class => 'onAccountOverviewPageLoaded',
        ];
    }

    /**
     * Add user management information to account overview
     */
    public function onAccountOverviewPageLoaded(AccountOverviewPageLoadedEvent $event): void
    {
        $customer = $event->getSalesChannelContext()->getCustomer();
        
        if (!$customer) {
            return;
        }

        // Check if customer is an order manager
        $isOrderManager = $this->userManagementService->isOrderManager($customer);
        
        // Add extension to indicate if user management should be shown
        $event->getPage()->addExtension('bsShowUserManagement', $isOrderManager);
    }
}
