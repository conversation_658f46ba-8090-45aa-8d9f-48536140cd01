<?php declare(strict_types=1);

namespace Bs\OrderContact\Subscriber;

use Bs\OrderContact\Service\UserManagementService;
use Shopware\Storefront\Page\Checkout\Confirm\CheckoutConfirmPageLoadedEvent;
use Shopware\Storefront\Page\Checkout\Finish\CheckoutFinishPageLoadedEvent;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;

/**
 * Class CheckoutPageSubscriber
 * @package BsOrderContact
 * <AUTHOR>
 * @link www.brainstreamtechnolabs.com
 */
class CheckoutPageSubscriber implements EventSubscriberInterface
{
    private UserManagementService $userManagementService;

    public function __construct(UserManagementService $userManagementService)
    {
        $this->userManagementService = $userManagementService;
    }

    public static function getSubscribedEvents(): array
    {
        return [
            CheckoutConfirmPageLoadedEvent::class => 'onCheckoutConfirmPageLoaded',
            CheckoutFinishPageLoadedEvent::class => 'onCheckoutFinishPageLoaded',
        ];
    }

    /**
     * Add contact persons to checkout confirm page
     */
    public function onCheckoutConfirmPageLoaded(CheckoutConfirmPageLoadedEvent $event): void
    {
        $customer = $event->getSalesChannelContext()->getCustomer();
        
        if (!$customer) {
            return;
        }

        // Get contact persons for the customer's email
        $contactPersons = $this->userManagementService->getContactPersonsForCheckout(
            $customer->getCustomerNumber(),
            $customer->getEmail(),
            $event->getContext()
        );

        // Add contact persons to page extension
        $event->getPage()->addExtension('bsContactPersons', $contactPersons);
    }

    /**
     * Add contact person information to finish page if available
     */
    public function onCheckoutFinishPageLoaded(CheckoutFinishPageLoadedEvent $event): void
    {
        $order = $event->getPage()->getOrder();

        if ($order && $order->getCustomFields()) {
            $customFields = $order->getCustomFields();
            if (isset($customFields['bs_contact_person_name'])) {
                $contactPersonInfo = [
                    'id' => $customFields['bs_contact_person_id'] ?? null,
                    'name' => $customFields['bs_contact_person_name'],
                ];
                $event->getPage()->addExtension('bsOrderContactPerson', $contactPersonInfo);
            }
        }
    }
}
