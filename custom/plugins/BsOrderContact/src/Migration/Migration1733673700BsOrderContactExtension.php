<?php declare(strict_types=1);

namespace Bs\OrderContact\Migration;

use Doctrine\DBAL\Connection;
use Shopware\Core\Framework\Migration\MigrationStep;

class Migration1733673700BsOrderContactExtension extends MigrationStep
{
    public function getCreationTimestamp(): int
    {
        return 1733673700;
    }

    public function update(Connection $connection): void
    {
        // No database schema changes needed - we'll use custom fields instead
        // This migration is kept for consistency but doesn't modify the database
    }

    public function updateDestructive(Connection $connection): void
    {
        // No destructive changes needed since we're using custom fields
    }
}
