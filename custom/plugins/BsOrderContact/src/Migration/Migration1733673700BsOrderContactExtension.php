<?php declare(strict_types=1);

namespace Bs\OrderContact\Migration;

use Doctrine\DBAL\Connection;
use Shopware\Core\Framework\Migration\MigrationStep;

class Migration1733673700BsOrderContactExtension extends MigrationStep
{
    public function getCreationTimestamp(): int
    {
        return 1733673700;
    }

    public function update(Connection $connection): void
    {
        $sql = <<<SQL
ALTER TABLE `order` 
ADD COLUMN `bs_contact_person_id` BINARY(16) NULL AFTER `custom_fields`,
ADD COLUMN `bs_contact_person_name` VARCHAR(255) COLLATE utf8mb4_unicode_ci NULL AFTER `bs_contact_person_id`,
ADD INDEX `idx_bs_contact_person_id` (`bs_contact_person_id`);
SQL;
        $connection->executeStatement($sql);
    }

    public function updateDestructive(Connection $connection): void
    {
        $sql = <<<SQL
ALTER TABLE `order` 
DROP INDEX IF EXISTS `idx_bs_contact_person_id`,
DROP COLUMN IF EXISTS `bs_contact_person_id`,
DROP COLUMN IF EXISTS `bs_contact_person_name`;
SQL;
        $connection->executeStatement($sql);
    }
}
