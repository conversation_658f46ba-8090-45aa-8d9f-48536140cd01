<?php declare(strict_types=1);

namespace Bs\OrderContact\Core\Content\ContactPerson;

use Shopware\Core\Framework\DataAbstractionLayer\EntityDefinition;
use Shopware\Core\Framework\DataAbstractionLayer\Field\CreatedAtField;
use Shopware\Core\Framework\DataAbstractionLayer\Field\Flag\ApiAware;
use Shopware\Core\Framework\DataAbstractionLayer\Field\Flag\PrimaryKey;
use Shopware\Core\Framework\DataAbstractionLayer\Field\Flag\Required;
use Shopware\Core\Framework\DataAbstractionLayer\Field\IdField;
use Shopware\Core\Framework\DataAbstractionLayer\Field\StringField;
use Shopware\Core\Framework\DataAbstractionLayer\Field\UpdatedAtField;
use Shopware\Core\Framework\DataAbstractionLayer\FieldCollection;

/**
 * Class ContactPersonDefinition
 * @package BsOrderContact
 * <AUTHOR>
 * @link www.brainstreamtechnolabs.com
 */
class ContactPersonDefinition extends EntityDefinition
{
    public const ENTITY_NAME = 'bs_order_contact_person';

    public function getEntityName(): string
    {
        return self::ENTITY_NAME;
    }

    public function getEntityClass(): string
    {
        return ContactPersonEntity::class;
    }

    public function getCollectionClass(): string
    {
        return ContactPersonCollection::class;
    }

    protected function defineFields(): FieldCollection
    {
        return new FieldCollection([
            (new IdField('id', 'id'))->addFlags(new Required(), new PrimaryKey(), new ApiAware()),
            (new StringField('customer_number', 'customerNumber', 255))->addFlags(new Required(), new ApiAware()),
            (new StringField('email', 'email', 254))->addFlags(new Required(), new ApiAware()),
            (new StringField('contact_person', 'contactPerson', 255))->addFlags(new Required(), new ApiAware()),
            (new CreatedAtField())->addFlags(new ApiAware()),
            (new UpdatedAtField())->addFlags(new ApiAware()),
        ]);
    }
}
